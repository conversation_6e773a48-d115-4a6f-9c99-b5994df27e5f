syntax = "proto3";

package audit;

service AuditService {
  rpc CreateAuditLog(CreateAuditLogRequest) returns (AuditLogResponse) {}
  rpc GetAuditLog(GetAuditLogRequest) returns (AuditLogResponse) {}
  rpc ListAuditLogs(ListAuditLogsRequest) returns (ListAuditLogsResponse) {}
  rpc SearchAuditLogs(SearchAuditLogsRequest) returns (ListAuditLogsResponse) {}
}

message AuditLog {
  int32 id = 1;
  int32 userId = 2;
  int64 orgId = 3;
  string userRole = 4;
  string serviceName = 5;
  string actions = 6;
  string resourceType = 7;
  int32 resourceId = 8;
  string description = 9;
  map<string, string> metadata = 10;
  string ipAddress = 11;
  string userAgent = 12;
  int64 createdAt = 13;
  string source = 14;
}

message CreateAuditLogRequest {
  int64 userId = 1;
  int64 orgId = 2;
  string userRole = 3;
  string actions = 4;
  string serviceName = 5;
  string resourceType = 6;
  int64 resourceId = 7;
  string description = 8;
  map<string, string> metadata = 9;
  string ipAddress = 10;
  string userAgent = 11;
  string source = 12;
}

message GetAuditLogRequest {
  int32 id = 1;
}

message AuditLogResponse {
  AuditLog auditLog = 1;
  string message = 2;
}

message ListAuditLogsRequest {
  int32 pageSize = 1;
  string pageToken = 2;
  string orderBy = 3;
}

message ListAuditLogsResponse {
  repeated AuditLog auditLogs = 1;
  string nextPageToken = 2;
  int32 totalSize = 3;
}

message SearchAuditLogsRequest {
  int32 userId = 1;
  string serviceName = 2;
  string actions = 3;
  string resourceType = 4;
  int32 resourceId = 5;
  int64 startDate = 6;
  int64 endDate = 7;
  int32 pageSize = 8;
  string pageToken = 9;
}
