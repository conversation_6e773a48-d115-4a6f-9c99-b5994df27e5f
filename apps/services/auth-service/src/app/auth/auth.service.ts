import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';
import { Client, ClientGrpc, Transport } from '@nestjs/microservices';
import { join } from 'path';
import { AppService } from '../app.service';
import { AuditClientService } from '../audit/audit.service';
import { InjectModel } from '@nestjs/sequelize';
import { User } from '../user/model/user.model';
import { Role } from '../user/model/role.model';
import { UserRole } from '../user/model/user-role.model';
import {
  GoogleAuthService,
  JwtService,
  OtpService,
} from '@apply-goal-backend/auth';
import { EnvUtil } from '@apply-goal-backend/utils';
import bcrypt from 'bcrypt';
import {
  CreateAuditLogRequest,
  LoginRequest,
  LoginResponse,
  LogoutRequest,
  LogoutResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  RegisterRequest,
  RegisterResponse,
  ValidateTokenRequest,
  ValidateTokenResponse,
  GenerateOtpRequest,
  GenerateOtpResponse,
  VerifyOtpRequest,
  VerifyOtpResponse,
  SsoAuthRequest,
  SsoAuthResponse,
  ForgotPasswordRequest,
  ForgotPasswordResponse,
  ResetPasswordRequest,
  ResetPasswordResponse,
  VerifyResetTokenRequest,
  VerifyResetTokenResponse,
} from './auth.interfaces';
import { Token } from './token.model';
import { PasswordResetToken } from './password-reset-token.model';
import { Op } from 'sequelize';
import { RmqPublisherService } from '@apply-goal-backend/messaging';
import { Department } from '../user/model/department.model';
import { OTP_REGISTER } from '@apply-goal-backend/common';
import { Feature } from '../user/model/feature.model';
import { SubFeature } from '../user/model/sub-feature.model';
import { Module } from '../user/model/module.model';
import { UserDepartment } from '../user/model/user‐department.model';
import { OrganizationService } from '../organization/organization.service';
import { Organization } from '../organization/organization.model';

// Students Service gRPC interface
interface StudentsService {
  createStudentAfterRegistration(data: any): any;
}

@Injectable()
export class AuthService implements OnModuleInit {
  private readonly logger = new Logger(AuthService?.name);
  private studentsService: StudentsService;

  @Client({
    transport: Transport.GRPC,
    options: {
      package: 'students',
      protoPath: join(
        process.cwd(),
        'libs/shared/dto/src/lib/students/students.proto'
      ),
      url: process.env.STUDENTS_SERVICE_URL || 'students-service:50058',
    },
  })
  private studentsClient: ClientGrpc;

  constructor(
    @InjectModel(User) private userModel: typeof User,
    @InjectModel(Role) private roleModel: typeof Role,
    @InjectModel(UserRole) private userRoleModel: typeof UserRole,
    @InjectModel(Token) private tokenModel: typeof Token,
    @InjectModel(PasswordResetToken)
    private passwordResetTokenModel: typeof PasswordResetToken,
    @InjectModel(Department) private departmentModel: typeof Department,
    @InjectModel(UserDepartment)
    private readonly userDeptModel: typeof UserDepartment, // ← inject it
    @InjectModel(Organization) private organizationModel: typeof Organization,
    private readonly appService: AppService,
    private readonly auditClient: AuditClientService,
    private readonly jwtService: JwtService,
    private readonly otpService: OtpService,
    private readonly rmqPublisher: RmqPublisherService,
    private readonly googleAuth: GoogleAuthService,
    private readonly organizationService: OrganizationService
  ) {}

  onModuleInit() {
    this.studentsService =
      this.studentsClient.getService<StudentsService>('StudentService');
  }

  private async createAuditLog(payload: CreateAuditLogRequest) {
    const start = Date.now();
    try {
      await firstValueFrom(this.auditClient.createAuditLog(payload));
      this.appService.trackAuditLogRequest(
        'create',
        'success',
        (Date.now() - start) / 1000
      );
    } catch (error) {
      this.appService.trackAuditLogRequest(
        'create',
        'error',
        (Date.now() - start) / 1000
      );
      this.logger.error('Failed to create audit log', error);
    }
  }

  private trackMetrics(
    operation: string,
    status: 'success' | 'failure' | 'error',
    duration: number
  ) {
    try {
      if (operation === 'token')
        this.appService.trackTokenOperation('generate', duration);
      else if (operation === 'login')
        this.appService.trackLoginAttempt(
          status,
          status === 'success' ? 'valid_credentials' : 'invalid_credentials'
        );
      else if (operation === 'auth')
        this.appService.trackAuthorization('api', 'access', status);
      if (status === 'success' && operation === 'login')
        this.appService.incrementActiveSessions('user');
    } catch (err) {
      this.logger.warn(`Metrics error: ${err.message}`);
    }
  }

  async registerGrpc(request: RegisterRequest): Promise<RegisterResponse> {
    const startTime = Date.now();
    let transaction: any;
    try {
      this.logger.debug(`gRPC Register for user: ${JSON.stringify(request)}`);
      transaction = await this.userModel.sequelize.transaction();

      const existingUser = await this.userModel.findOne({
        where: { email: request.email },
        transaction,
      });

      this.logger.debug(
        `Existing user: ${JSON.stringify(existingUser, null, 2)}`
      );

      if (existingUser) {
        await transaction.rollback();
        return {
          success: false,
          message: 'User already exists',
        };
      }

      const deptRow = await this.departmentModel.findOne({
        where: { name: request.departmentName },
        transaction,
      });

      this.logger.debug(
        `Department found: ${JSON.stringify(deptRow, null, 2)}`
      );

      let organization;
      // Handle organization lookup
      if (request.organizationName) {
        organization = await this.organizationModel.findOne({
          where: { name: request.organizationName },
          transaction,
        });
      } else {
        organization = await this.organizationModel.findOne({
          where: { name: 'ApplyGoal' },
          transaction,
        });
      }

      if (!organization) {
        // create organization
        organization = await this.organizationModel.create(
          {
            name: request.organizationName,
            type: 'agency',
            description: `Organization for ${request.organizationName}`,
          },
          { transaction }
        );
      }

      this.logger.debug(
        `Organization found: ${JSON.stringify(organization, null, 2)}`
      );

      // hash password
      const hashedPassword = await bcrypt.hash(
        request.password,
        EnvUtil.getNumber('HASH_SALT', 10)
      );

      // create user
      const user = await this.userModel.create(
        {
          name: request?.name,
          email: request.email,
          password: hashedPassword,
          phone: request.phone || null,
          nationality: request.nationality || null,
          presentCountry: request.nationality || null,
          permanentCountry: request.nationality || null,
          organizationId: organization.id,
        },
        { transaction }
      );

      this.logger.debug(
        `User created successfully: ${JSON.stringify(user, null, 2)}`
      );

      const role = await this.roleModel.findOne({
        where: {
          name:
            request.roleName === 'Agency' ? 'Agency Admin' : request.roleName,
        },
        transaction,
      });

      if (!role) {
        this.logger.log(`Role not found: ${request.roleName}`);
        await transaction.rollback();
        return {
          success: false,
          message: 'Role not found',
        };
      }

      await this.userRoleModel.create(
        {
          userId: user.id,
          roleId: role.id,
        },
        { transaction }
      );

      // 5) **Assign their department** via the join table
      if (deptRow) {
        await this.userDeptModel.create(
          { userId: user.id, departmentId: deptRow.id },
          { transaction }
        );
      }

      // Commit transaction
      await transaction.commit();
      transaction = null;

      // Send verification email for different roles
      if (['Student', 'Agency'].includes(request.roleName)) {
        try {
          // Generate OTP for email verification
          const otpPayload = await this.otpService.generateOtp({
            identifier: `${OTP_REGISTER}:${request.email}`,
            length: 6,
            expiresInSeconds: 300, // 5 minutes
          });

          // Send verification email with OTP
          await this.rmqPublisher.sendEmail({
            to: request.email,
            subject: 'welcome to ApplyGoal',
            templateId: 'welcome-2fa',
            templateData: {
              name: request?.name,
              otp: otpPayload.otp,
            },
          });
          // Create student / university / agency profile based on role
          try {
            if (role.name === 'Student') {
              this.logger.log(`Creating student profile for user: ${user.id}`);
              const studentResult = await firstValueFrom(
                this.studentsService.createStudentAfterRegistration({
                  user_id: user.id.toString(),
                  name: request.name,
                  email: request.email,
                  phone: request.phone,
                  nationality: request.nationality,
                  organizationName: request.organizationName,
                  roleName: request.roleName,
                  departmentName: request.departmentName,
                })
              );
              this.logger.log(
                `Student profile created successfully: ${
                  (studentResult as any)?.student?.id
                }`
              );
            }
            if (role.name === 'Agent Admin') {
              this.logger.log(
                `Creating university profile for user: ${user.id}`
              );
              // TODO: create agency as inactive
            }
          } catch (profileError) {
            this.logger.error(
              `Failed to create ${role.name} profile: ${profileError.message}`,
              profileError.stack
            );
            // Continue with registration even if profile creation fails
          }

          this.logger.log(`Verification email sent to ${request.email}`);
        } catch (emailError) {
          this.logger.error(
            `Failed to send verification email: ${emailError.message}`,
            emailError.stack
          );
          // Continue with registration even if email fails
        }
      }

      this.trackMetrics('login', 'success', Date.now() - startTime);

      await this.createAuditLog({
        userId: Number(user.id),
        orgId: user.organizationId,
        userRole: role?.name,
        actions: 'REGISTER',
        serviceName: 'auth-service',
        resourceType: 'USER',
        resourceId: Number(user.id),
        description: `User ${request.email} registered via gRPC`,
        metadata: { ip: request.ipAddress || '' },
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });

      return {
        success: true,
        message:
          'User registered successfully. Please check your email to verify your account.',
      };
    } catch (error) {
      if (transaction) {
        try {
          await transaction.rollback();
        } catch (rollbackError) {
          this.logger.error('Transaction rollback failed', rollbackError);
        }
      }
      this.trackMetrics('login', 'failure', Date.now() - startTime);
      this.logger.error(`Registration error: ${error.message}`, error.stack);
      return {
        success: false,
        message: 'Registration failed: ' + error.message,
      };
    }
  }

  async loginGrpc(request: LoginRequest): Promise<LoginResponse> {
    const startTime = Date.now();
    try {
      this.logger.log(`gRPC Login for user: ${request.email}`);

      const user = await this.getUserWithAssociations(request.email);

      if (!user || !(await bcrypt.compare(request.password, user.password))) {
        throw new Error('Invalid credentials');
      }

      if (user.status !== 'active') {
        throw new Error('User account is not active');
      }

      const accessToken = await this.jwtService.generateAccessToken({
        sub: user.id,
        email: user.email,
        roles: user.roles.map((r) => r?.name?.replace(/\s+/g, '')),
        departments: user.departments.map((d) => d?.name?.replace(/\s+/g, '')),
        permissions: Array.from(this.getAllPermissions(user)),
        organizationId: user.organizationId,
        type: 'access',
      });

      const refreshToken = await this.jwtService.generateRefreshToken(user.id);

      // Save token to DB
      await this.tokenModel.create({
        userId: user.id,
        refreshToken: refreshToken,
        accessToken: accessToken,
        refreshTokenExpiresAt: new Date(
          Date.now() +
            EnvUtil.getNumber('REFRESH_EXPIRY_MS', 7 * 24 * 60 * 60 * 1000)
        ),
        accessTokenExpiresAt: new Date(
          Date.now() + EnvUtil.getNumber('ACCESS_EXPIRY_MS', 1 * 60 * 60 * 1000)
        ),
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
      });

      this.trackMetrics('login', 'success', Date.now() - startTime);

      await this.createAuditLog({
        userId: Number(user.id),
        orgId: user.organizationId,
        userRole: user.roles[0]?.name || 'user',
        actions: 'LOGIN',
        serviceName: 'auth-service',
        resourceType: 'USER',
        resourceId: Number(user.id),
        description: `User ${request.email} logged in via gRPC`,
        metadata: { ip: request.ipAddress || '' },
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });

      this.appService.incrementActiveSessions('user');

      // this.logger.debug(
      //   `Login successful for user: ${JSON.stringify(
      //     this.serializeUser(user),
      //     null,
      //     2
      //   )}`
      // );

      // this.logger.debug(`User raw data: ${JSON.stringify(user, null, 2)}`);

      return {
        success: true,
        message: 'Login successful',
        accessToken: accessToken,
        refreshToken: refreshToken,
        user: this.serializeUser(user),
      };
    } catch (error) {
      this.trackMetrics('login', 'failure', Date.now() - startTime);
      return { success: false, message: 'Login failed: ' + error.message };
    }
  }

  async logoutGrpc(request: LogoutRequest): Promise<LogoutResponse> {
    const start = Date.now();
    this.logger.log(`gRPC Logout for token: ${request.accessToken}`);
    this.appService.decrementActiveSessions('user');

    await this.tokenModel.update(
      { revoked: true },
      {
        where: { accessToken: request.accessToken },
      }
    );

    await this.createAuditLog({
      userId: Number(0),
      userRole: 'user',
      actions: 'LOGOUT',
      serviceName: 'auth-service',
      resourceType: 'USER',
      resourceId: Number(0),
      description: `User logged out via gRPC`,
      source: 'grpc',
    });

    this.trackMetrics('token', 'success', Date.now() - start);
    return { success: true, message: 'Logout successful' };
  }

  async validateTokenGrpc(
    request: ValidateTokenRequest
  ): Promise<ValidateTokenResponse> {
    const start = Date.now();
    try {
      this.logger.log(`gRPC ValidateToken for token: ${request.token}`);

      // 1. Decode and verify the JWT token
      await this.jwtService.verifyToken(request.token); // Throws on invalid

      // 2. Check if token is valid and not revoked in DB
      const dbToken = await this.tokenModel.findOne({
        where: {
          accessToken: request.token,
          revoked: false,
        },
        include: [
          {
            model: this.userModel,
            include: [{ model: this.roleModel, as: 'roles' }],
          },
        ],
      });

      if (!dbToken || !dbToken.user) {
        throw new Error('Token is revoked or expired');
      }

      this.trackMetrics('auth', 'success', Date.now() - start);

      // 5. Return response with user info
      const user = dbToken.user;

      await this.createAuditLog({
        userId: Number(user.id),
        orgId: user.organizationId,
        userRole: 'user',
        actions: 'VALIDATE_TOKEN',
        serviceName: 'auth-service',
        resourceType: 'TOKEN',
        resourceId: Number(user.id),
        description: `Token validated via gRPC`,
        source: 'grpc',
      });

      return {
        valid: true,
        message: 'Token is valid',
        user: {
          id: user.id,
          username: user?.name,
          name: user?.name,
          email: user.email,
          roles: user.roles.map((r) => ({
            id: r.id,
            name: r?.name,
            // status: r.status,
            // permissions: r.permissions || [],
          })),
        },
      };
    } catch (error) {
      this.trackMetrics('auth', 'failure', Date.now() - start);
      return {
        valid: false,
        message: 'Token validation failed: ' + error.message,
      };
    }
  }

  async refreshTokenGrpc(
    request: RefreshTokenRequest
  ): Promise<RefreshTokenResponse> {
    const start = Date.now();
    try {
      this.logger.log(`gRPC RefreshToken for token: ${request.refreshToken}`);

      // 1. Validate the refresh token in DB
      const oldToken = await this.tokenModel.findOne({
        where: {
          refreshToken: request.refreshToken,
          revoked: false,
          refreshTokenExpiresAt: { [Op.gt]: new Date() },
        },
        include: [
          {
            model: this.userModel,
            include: [
              { model: this.roleModel, as: 'roles' },
              { model: this.departmentModel, as: 'departments' },
              { model: this.organizationModel, as: 'organization' },
            ],
          },
        ],
      });

      if (!oldToken || !oldToken.user) {
        throw new Error('Invalid or expired refresh token');
      }

      const user = oldToken.user;

      // 2. Revoke the old token
      oldToken.revoked = true;
      await oldToken.save();

      // 3. Generate new access & refresh tokens
      const accessToken = await this.jwtService.generateAccessToken({
        sub: user.id,
        email: user.email,
        roles: user.roles.map((r) => r?.name?.replace(/\s+/g, '')),
        departments: user.departments?.map((d) => d?.name?.replace(/\s+/g, '')),
        permissions: Array.from(this.getAllPermissions(user)),
        organizationId: user.organizationId,
        type: 'access',
      });

      const refreshToken = await this.jwtService.generateRefreshToken(user.id);

      // 4. Save new token record
      const now = new Date();
      const accessExpiry = new Date(
        now.getTime() + EnvUtil.getNumber('ACCESS_EXPIRY_MS', 60 * 60 * 1000)
      );
      const refreshExpiry = new Date(
        now.getTime() +
          EnvUtil.getNumber('REFRESH_EXPIRY_MS', 7 * 24 * 60 * 60 * 1000)
      );

      await this.tokenModel.create({
        userId: user.id,
        accessToken: accessToken,
        refreshToken: refreshToken,
        accessTokenExpiresAt: accessExpiry,
        refreshTokenExpiresAt: refreshExpiry,
        ipAddress: oldToken.ipAddress,
        userAgent: oldToken.userAgent,
      });

      // 5. Audit log
      await this.createAuditLog({
        userId: Number(user.id),
        userRole: user.roles[0]?.name || 'user',
        actions: 'refreshToken',
        serviceName: 'auth-service',
        resourceType: 'TOKEN',
        resourceId: Number(user.id),
        description: `Refresh token used via gRPC`,
        metadata: {},
        ipAddress: oldToken.ipAddress || '',
        userAgent: oldToken.userAgent || '',
        source: 'grpc',
      });

      this.trackMetrics('token', 'success', Date.now() - start);

      return {
        success: true,
        message: 'Token refreshed successfully',
        accessToken: accessToken,
        refreshToken: refreshToken,
      };
    } catch (error) {
      this.trackMetrics('token', 'failure', Date.now() - start);
      return {
        success: false,
        message: 'Token refresh failed: ' + error.message,
      };
    }
  }

  async generateOtpGrpc(
    request: GenerateOtpRequest
  ): Promise<GenerateOtpResponse> {
    const startTime = Date.now();
    try {
      this.logger.log(
        `gRPC GenerateOtp for email: ${request.email}, type: ${request.type}`
      );

      // Find the user
      const user = await this.userModel.findOne({
        where: { email: request.email },
      });

      if (!user) {
        return {
          success: false,
          message: 'User not found',
        };
      }

      // Generate OTP
      const otpPayload = await this.otpService.generateOtp({
        identifier: `${request.type}:${request.email}`,
        length: 6,
        expiresInSeconds: 300, // 5 minutes
      });

      // TODO: Send the OTP via email or SMS
      await this.rmqPublisher.sendEmail({
        to: request.email,
        subject: 'Your OTP Code',
        templateId: 'welcome-2fa',
        templateData: {
          name: user?.name,
          otp: otpPayload.otp,
        },
      });
      this.logger.debug(
        `Generated OTP for ${request.email}: ${otpPayload.otp}`
      );

      // Create audit log
      await this.createAuditLog({
        userId: Number(user.id),
        orgId: user.organizationId,
        userRole: 'user', // You might want to get the actual role
        actions: 'GENERATE_OTP',
        serviceName: 'auth-service',
        resourceType: 'OTP',
        resourceId: Number(user.id),
        description: `OTP generated for ${request.email} (${request.type})`,
        metadata: { type: request.type },
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });

      this.trackMetrics('otp', 'success', Date.now() - startTime);

      return {
        success: true,
        message: 'OTP sent successfully',
        expiresAt: otpPayload.expiresAt,
      };
    } catch (error) {
      this.trackMetrics('otp', 'error', Date.now() - startTime);
      this.logger.error(`GenerateOtp error: ${error.message}`, error.stack);
      return {
        success: false,
        message: 'Failed to generate OTP: ' + error.message,
      };
    }
  }

  async verifyOtpGrpc(request: VerifyOtpRequest): Promise<VerifyOtpResponse> {
    const startTime = Date.now();
    try {
      this.logger.log(
        `gRPC VerifyOtp for email: ${request.email}, type: ${request.type}`
      );

      // Find the user
      const user = await this.getUserWithAssociations(request.email);

      if (!user) {
        return {
          success: false,
          message: 'User not found',
        };
      }

      // Verify OTP
      const isValid = await this.otpService.verifyOtp(
        `${request.type}:${request.email}`,
        request.otp
      );

      this.logger.debug(
        `OTP verification for ${request.email} (${request.type}): ${isValid}`
      );

      if (!isValid) {
        this.trackMetrics('otp', 'failure', Date.now() - startTime);
        return {
          success: false,
          message: 'Invalid or expired OTP',
        };
      }

      // OTP verified successfully & activate user status
      await this.userModel.update(
        { status: 'active' },
        {
          where: { id: user.id },
        }
      );
      this.logger.log(`User ${request.email} verified successfully`);

      // Create audit log
      await this.createAuditLog({
        userId: Number(user.id),
        orgId: user.organizationId,
        userRole: user.roles[0]?.name || 'user',
        actions: 'VERIFY_OTP',
        serviceName: 'auth-service',
        resourceType: 'OTP',
        resourceId: Number(user.id),
        description: `OTP verified for ${request.email} (${request.type})`,
        metadata: { type: request.type },
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });

      this.trackMetrics('otp', 'success', Date.now() - startTime);

      return {
        success: true,
        message: 'OTP verified successfully',
      };
    } catch (error) {
      this.trackMetrics('otp', 'error', Date.now() - startTime);
      this.logger.error(`VerifyOtp error: ${error.message}`, error.stack);
      return {
        success: false,
        message: 'Failed to verify OTP: ' + error.message,
      };
    }
  }

  async ssoAuthGrpc(request: SsoAuthRequest): Promise<SsoAuthResponse> {
    const start = Date.now();
    try {
      this.logger.log(`gRPC SsoAuth for provider: ${request.provider}`);

      let email: string;
      let firstName: string;
      let lastName: string;

      if (request.provider === 'google') {
        const payload = await this.googleAuth.verifyIdToken(request.token);
        email = payload.email!;
        [firstName, lastName] = payload?.name?.split(' ') ?? [
          'Unknown',
          'User',
        ];
      } else {
        throw new Error('Unsupported SSO provider');
      }

      // Find or create user
      let user = await this.getUserWithAssociations(request.email);
      let isNewUser = false;
      const deptRow = await this.departmentModel.findOne({
        where: { name: 'Student' },
      });

      if (!user) {
        user = await this.userModel.create({
          firstName,
          lastName,
          email,
          password: null,
          status: 'active',
          deptId: deptRow ? deptRow.id : null,
        });
        isNewUser = true;

        const role = await this.roleModel.findOne({
          where: { name: 'Student' },
        });
        if (role) {
          await this.userRoleModel.create({
            userId: user.id,
            roleId: role.id,
          });
        }
      }

      const roles = await user.$get('roles', {
        include: [{ association: 'permissions' }],
      });

      const accessToken = await this.jwtService.generateAccessToken({
        sub: user.id,
        email: user.email,
        roles: user.roles.map((r) => r?.name?.replace(/\s+/g, '')),
        departments: user.departments?.map((d) => d?.name?.replace(/\s+/g, '')),
        permissions: Array.from(this.getAllPermissions(user)),
        organizationId: user.organizationId,
        type: 'access',
      });

      const refreshToken = await this.jwtService.generateRefreshToken(user.id);

      await this.tokenModel.create({
        userId: user.id,
        refreshToken,
        accessToken,
        refreshTokenExpiresAt: new Date(Date.now() + 7 * 86400000),
        accessTokenExpiresAt: new Date(Date.now() + 3600000),
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
      });

      // 🧠 Track metrics
      this.trackMetrics('login', 'success', Date.now() - start);
      this.appService.incrementActiveSessions('user');

      // 📝 Audit log
      await this.createAuditLog({
        userId: Number(user.id),
        orgId: user.organizationId,
        userRole: roles[0]?.name || 'user',
        actions: isNewUser ? 'REGISTER' : 'LOGIN',
        serviceName: 'auth-service',
        resourceType: 'USER',
        resourceId: Number(user.id),
        description: `${isNewUser ? 'Registered' : 'Logged in'} via SSO (${
          request.provider
        })`,
        metadata: { provider: request.provider },
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });

      return {
        success: true,
        message: 'SSO login successful',
        accessToken,
        refreshToken,
        user: this.serializeUser(user),
      };
    } catch (error) {
      this.trackMetrics('login', 'failure', Date.now() - start);
      this.logger.error(`SSO login error: ${error.message}`, error.stack);
      return {
        success: false,
        message: 'SSO login failed: ' + error.message,
      };
    }
  }

  async getUserWithAssociations(email: string) {
    return this.userModel.findOne({
      where: { email },

      include: [
        // 1) Roles + **all** features & subFeatures (with their isAllowed flag)
        {
          model: Role, // ← no more .scope('withPermissions')
          as: 'roles',
          include: [
            {
              model: Feature,
              as: 'features',
              through: { attributes: ['isAllowed'] },
              include: [{ model: Module, as: 'module' }],
            },
            {
              model: SubFeature,
              as: 'subFeatures',
              through: { attributes: ['isAllowed'] },
              include: [
                {
                  model: Feature,
                  as: 'feature',
                  include: [{ model: Module, as: 'module' }],
                },
              ],
            },
          ],
        },

        // 2) Departments + their allowed features & subFeatures
        {
          model: Department,
          as: 'departments',
          include: [
            {
              model: Feature,
              as: 'features',
              through: { attributes: ['isAllowed'] },
              include: [{ model: Module, as: 'module' }],
            },
            {
              model: SubFeature,
              as: 'subFeatures',
              through: { attributes: ['isAllowed'] },
              include: [
                {
                  model: Feature,
                  as: 'feature',
                  include: [{ model: Module, as: 'module' }],
                },
              ],
            },
          ],
        },

        // 3) Organization
        {
          model: this.organizationModel,
          as: 'organization',
        },
      ],
    });
  }

  private serializeUser(user: any) {
    return {
      id: user.id,
      name: user.name,
      email: user.email,
      roles: user.roles.map((r: any) => ({
        id: r.id,
        name: r?.name,
        features: r.features?.map((f: any) => ({
          id: f.id,
          name: f.name,
          permission: f.RoleFeaturePermission?.isAllowed,
          module: {
            id: f.module.id,
            name: f.module.name,
          },
        })),
        subFeatures: r.subFeatures?.map((sf: any) => ({
          id: sf.id,
          name: sf.name,
          permission: sf.RoleSubFeaturePermission?.isAllowed,
        })),
      })),
      departments: user.departments?.map((d: any) => ({
        id: d.id,
        name: d.name,
        children: d.children?.map((c: any) => ({
          id: c.id,
          name: c.name,
        })),
      })),
    };
  }

  private getAllPermissions(user: any): Set<string> {
    const perms = new Set<string>();

    const normalize = (s: string) => s.replace(/\s+/g, '');

    // 1) From Roles
    for (const role of user.roles) {
      // Features
      for (const feat of role.features || []) {
        if (
          feat.RoleFeaturePermission?.isAllowed &&
          feat.module?.name &&
          feat.name
        ) {
          perms.add(`${normalize(feat.module.name)}:${normalize(feat.name)}`);
        }
      }
      // Sub-Features
      for (const sf of role.subFeatures || []) {
        if (
          sf.RoleSubFeaturePermission?.isAllowed &&
          sf.feature?.module?.name &&
          sf.feature?.name &&
          sf.name
        ) {
          perms.add(
            `${normalize(sf.feature.module.name)}:${normalize(
              sf.feature.name
            )}:${normalize(sf.name)}`
          );
        }
      }
    }

    // 2) From Departments
    for (const dept of user.departments || []) {
      // Features
      for (const feat of dept.features || []) {
        if (
          feat.DepartmentFeaturePermission?.isAllowed &&
          feat.module?.name &&
          feat.name
        ) {
          perms.add(`${normalize(feat.module.name)}:${normalize(feat.name)}`);
        }
      }
      // Sub-Features
      for (const sf of dept.subFeatures || []) {
        if (
          sf.DepartmentSubFeaturePermission?.isAllowed &&
          sf.feature?.module?.name &&
          sf.feature?.name &&
          sf.name
        ) {
          perms.add(
            `${normalize(sf.feature.module.name)}:${normalize(
              sf.feature.name
            )}:${normalize(sf.name)}`
          );
        }
      }
    }

    return perms;
  }

  // ─── Password Reset Methods ─────────────────────────────────────────────────
  async forgotPassword(
    request: ForgotPasswordRequest
  ): Promise<ForgotPasswordResponse> {
    const startTime = Date.now();
    try {
      this.logger.log(`Forgot password request for email: ${request.email}`);

      // Check if user exists
      const user = await this.userModel.findOne({
        where: { email: request.email },
      });

      if (!user) {
        // For security, don't reveal if email exists or not
        this.logger.warn(
          `Forgot password attempt for non-existent email: ${request.email}`
        );
        return {
          success: true,
          message:
            'If the email exists in our system, you will receive a password reset link.',
        };
      }

      // Generate secure reset token
      const resetToken = this.generateSecureToken();
      const expiresAt = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now

      // Invalidate any existing reset tokens for this email
      await this.passwordResetTokenModel.update(
        { used: true, usedAt: new Date() },
        { where: { email: request.email, used: false } }
      );

      // Create new reset token
      await this.passwordResetTokenModel.create({
        email: request.email,
        token: resetToken,
        expiresAt,
        userId: user.id,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
      });

      // Send password reset email
      await this.sendPasswordResetEmail(user.email, user.name, resetToken);

      // Create audit log
      await this.createAuditLog({
        userId: Number(user.id),
        userRole: 'User',
        actions: 'PASSWORD_RESET_REQUESTED',
        serviceName: 'auth-service',
        resourceType: 'password_reset',
        resourceId: Number(user.id),
        description: `Password reset requested for user ${user.email}`,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
      });

      this.trackMetrics('forgot_password', 'success', Date.now() - startTime);

      return {
        success: true,
        message:
          'If the email exists in our system, you will receive a password reset link.',
        resetToken: resetToken, // Only for development
      };
    } catch (error) {
      this.logger.error(`Forgot password error: ${error.message}`, error.stack);
      this.trackMetrics('forgot_password', 'error', Date.now() - startTime);
      throw new Error('Failed to process password reset request');
    }
  }

  async verifyResetToken(
    request: VerifyResetTokenRequest
  ): Promise<VerifyResetTokenResponse> {
    try {
      this.logger.log(`Verifying reset token for email: ${request.email}`);

      const resetToken = await this.passwordResetTokenModel.findOne({
        where: {
          email: request.email,
          token: request.resetToken,
          used: false,
        },
      });

      if (!resetToken) {
        return {
          valid: false,
          message: 'Invalid or expired reset token',
        };
      }

      if (resetToken.isExpired()) {
        return {
          valid: false,
          message: 'Reset token has expired',
        };
      }

      return {
        valid: true,
        message: 'Reset token is valid',
        expiresAt: resetToken.expiresAt,
      };
    } catch (error) {
      this.logger.error(
        `Verify reset token error: ${error.message}`,
        error.stack
      );
      return {
        valid: false,
        message: 'Failed to verify reset token',
      };
    }
  }

  async resetPassword(
    request: ResetPasswordRequest
  ): Promise<ResetPasswordResponse> {
    const startTime = Date.now();
    let transaction: any;

    try {
      this.logger.log(`Reset password request for email: ${request.email}`);

      // Validate passwords match
      if (request.newPassword !== request.confirmPassword) {
        return {
          success: false,
          message: 'Passwords do not match',
        };
      }

      // Validate password strength
      if (!this.isValidPassword(request.newPassword)) {
        return {
          success: false,
          message:
            'Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character',
        };
      }

      // Start transaction
      transaction = await this.userModel.sequelize.transaction();

      // Find and validate reset token
      const resetToken = await this.passwordResetTokenModel.findOne({
        where: {
          email: request.email,
          token: request.resetToken,
          used: false,
        },
        transaction,
      });

      if (!resetToken || !resetToken.isValid()) {
        await transaction.rollback();
        return {
          success: false,
          message: 'Invalid or expired reset token',
        };
      }

      // Find user
      const user = await this.userModel.findOne({
        where: { email: request.email },
        transaction,
      });

      if (!user) {
        await transaction.rollback();
        return {
          success: false,
          message: 'User not found',
        };
      }

      // Hash new password
      const hashedPassword = await bcrypt.hash(request.newPassword, 10);

      // Update user password
      await user.update({ password: hashedPassword }, { transaction });

      // Mark reset token as used
      await resetToken.update(
        { used: true, usedAt: new Date() },
        { transaction }
      );

      // Invalidate all existing tokens for this user (force re-login)
      await this.tokenModel.update(
        { revoked: true },
        { where: { userId: user.id }, transaction }
      );

      // Commit transaction
      await transaction.commit();

      // Send password change confirmation email
      await this.sendPasswordChangeConfirmationEmail(user.email, user.name);

      // Create audit log
      await this.createAuditLog({
        userId: Number(user.id),
        userRole: 'User',
        actions: 'PASSWORD_RESET_COMPLETED',
        serviceName: 'auth-service',
        resourceType: 'password_reset',
        resourceId: Number(user.id),
        description: `Password successfully reset for user ${user.email}`,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
      });

      this.trackMetrics('reset_password', 'success', Date.now() - startTime);

      return {
        success: true,
        message:
          'Password has been successfully reset. Please log in with your new password.',
      };
    } catch (error) {
      if (transaction) {
        await transaction.rollback();
      }
      this.logger.error(`Reset password error: ${error.message}`, error.stack);
      this.trackMetrics('reset_password', 'error', Date.now() - startTime);
      return {
        success: false,
        message: 'Failed to reset password. Please try again.',
      };
    }
  }

  // ─── Helper Methods ─────────────────────────────────────────────────
  private generateSecureToken(): string {
    const crypto = require('crypto');
    return crypto.randomBytes(32).toString('hex');
  }

  private isValidPassword(password: string): boolean {
    // Password must be at least 8 characters long and contain:
    // - At least one uppercase letter
    // - At least one lowercase letter
    // - At least one number
    // - At least one special character
    const passwordRegex =
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    return passwordRegex.test(password);
  }

  private async sendPasswordResetEmail(
    email: string,
    name: string,
    resetToken: string
  ): Promise<void> {
    try {
      const resetUrl = `${
        process.env.FRONTEND_URL || 'http://localhost:3000'
      }/reset-password?token=${resetToken}&email=${encodeURIComponent(email)}`;

      const emailPayload = {
        to: email,
        subject: 'Password Reset Request - ApplyGoal',
        template: 'password-reset',
        data: {
          name,
          resetUrl,
          expiryTime: '1 hour',
          supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
        },
      };

      await this.rmqPublisher.sendEmail(emailPayload);
      this.logger.log(`Password reset email sent to: ${email}`);
    } catch (error) {
      this.logger.error(
        `Failed to send password reset email to ${email}:`,
        error
      );
      // Don't throw error here as the reset token is still valid
    }
  }

  private async sendPasswordChangeConfirmationEmail(
    email: string,
    name: string
  ): Promise<void> {
    try {
      const emailPayload = {
        to: email,
        subject: 'Password Changed Successfully - ApplyGoal',
        template: 'password-change-confirmation',
        data: {
          name,
          changeTime: new Date().toLocaleString(),
          supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
        },
      };

      await this.rmqPublisher.sendEmail(emailPayload);
      this.logger.log(`Password change confirmation email sent to: ${email}`);
    } catch (error) {
      this.logger.error(
        `Failed to send password change confirmation email to ${email}:`,
        error
      );
      // Don't throw error here as the password was already changed successfully
    }
  }

  // ─── Cleanup Methods ─────────────────────────────────────────────────
  async cleanupExpiredResetTokens(): Promise<void> {
    try {
      const result = await this.passwordResetTokenModel.destroy({
        where: {
          expiresAt: {
            [Op.lt]: new Date(),
          },
        },
      });
      this.logger.log(`Cleaned up ${result} expired password reset tokens`);
    } catch (error) {
      this.logger.error('Failed to cleanup expired reset tokens:', error);
    }
  }
}
