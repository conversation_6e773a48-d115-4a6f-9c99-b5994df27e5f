import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { firstValueFrom } from 'rxjs';
import { AppService } from '../app.service';
import { AuditClientService } from '../audit/audit.service';
import { User } from './model/user.model';
import { Role } from './model/role.model';
import { UserRole } from './model/user-role.model';
import { Op, UniqueConstraintError } from 'sequelize';
import * as bcrypt from 'bcrypt';
import { EnvUtil } from '@apply-goal-backend/utils';
import { Module } from './model/module.model';
import { Department } from './model/department.model';
import { CreateAuditLogRequest } from '../auth/auth.interfaces';
import { Feature } from './model/feature.model';
import { SubFeature } from './model/sub-feature.model';
import { Transaction } from 'sequelize';
import { status } from '@grpc/grpc-js';
import { RpcException } from '@nestjs/microservices/exceptions';
import { RoleFeaturePermission } from './model/role-feature-permission.model';
import { RoleSubFeaturePermission } from './model/role-sub-feature-permission.model';
import {
  CreateRoleResponse,
  CreateRoleWithDetailsRequest,
  DeleteRoleRequest,
  DeleteRoleResponse,
  GetRoleDetailsRequest,
  GetRolesWithDetailsResponse,
  RoleDetailsResponse,
  UpdateRoleRequest,
  UpdateRoleResponse,
} from './user.interface';
import { Organization } from '../organization/organization.model';

@Injectable()
export class UserService {
  private readonly logger = new Logger(UserService.name);

  constructor(
    @InjectModel(User) private userModel: typeof User,
    @InjectModel(Role) private roleModel: typeof Role,
    @InjectModel(UserRole) private userRoleModel: typeof UserRole,
    @InjectModel(Module) private moduleModel: typeof Module,
    @InjectModel(Feature) private featureModel: typeof Feature,
    @InjectModel(SubFeature) private subFeatureModel: typeof SubFeature,
    @InjectModel(Department) private departmentModel: typeof Department,
    @InjectModel(Department)
    private userDepartmentModel: typeof Department,
    @InjectModel(RoleFeaturePermission)
    private roleFeaturePermissionModel: typeof RoleFeaturePermission,
    @InjectModel(RoleSubFeaturePermission)
    private roleSubFeaturePermissionModel: typeof RoleSubFeaturePermission,
    private readonly appService: AppService,
    private readonly auditClient: AuditClientService,
    @InjectModel(Organization)
    private organizationModel: typeof Organization
  ) {}

  // #region
  private async createAuditLog(payload: CreateAuditLogRequest) {
    const start = Date.now();
    try {
      await firstValueFrom(this.auditClient.createAuditLog(payload));
      this.appService.trackAuditLogRequest(
        'create',
        'success',
        (Date.now() - start) / 1000
      );
    } catch (error) {
      this.appService.trackAuditLogRequest(
        'create',
        'error',
        (Date.now() - start) / 1000
      );
      this.logger.error('Failed to create audit log', error);
    }
  }

  private trackMetrics(
    operation: string,
    status: 'success' | 'failure' | 'error',
    duration: number
  ) {
    try {
      if (operation === 'token')
        this.appService.trackTokenOperation('generate', duration);
      else if (operation === 'login')
        this.appService.trackLoginAttempt(
          status,
          status === 'success' ? 'valid_credentials' : 'invalid_credentials'
        );
      else if (operation === 'auth')
        this.appService.trackAuthorization('api', 'access', status);
      if (status === 'success' && operation === 'login')
        this.appService.incrementActiveSessions('user');
    } catch (err) {
      this.logger.warn(`Metrics error: ${err.message}`);
    }
  }
  // #endregion

  // ------- User/Employee/Student(Agency)----------------
  // #region

  // #endregion
  // ------- Modules -----------------------
  // #region
  /**
   * RPC: CreateModule(CreateModuleRequest)
   * request.name
   * request.features: [{ name, subFeatures:[{name},…] }, …]
   */
  async createModule(request: {
    name: string;
    userId?: number;
    roleName?: string;
    ipAddress?: string;
    userAgent?: string;
  }) {
    const t0 = Date.now();
    let tx: Transaction;
    let committed = false;
    let moduleRow;

    try {
      tx = await this.moduleModel.sequelize.transaction();

      // 1) check duplicate
      moduleRow = await this.moduleModel.findOne({
        where: { name: request.name },
        transaction: tx,
      });
      if (moduleRow) {
        await tx.rollback();
        return {
          success: false,
          message: 'Module already exists',
          module: moduleRow,
        };
      }

      // 2) create
      moduleRow = await this.moduleModel.create(
        { name: request.name },
        { transaction: tx }
      );

      await tx.commit();
      committed = true;
    } catch (dbErr) {
      if (tx && !committed) {
        try {
          await tx.rollback();
        } catch (rbErr) {
          this.logger.error('rollback failed', rbErr);
        }
      }
      this.trackMetrics('module_create', 'failure', Date.now() - t0);
      this.logger.error(
        `Failed to create module: ${dbErr.message}`,
        dbErr.stack
      );
      throw new RpcException({
        code: status.INTERNAL,
        message: dbErr.message,
      });
    }

    // outside transaction
    this.trackMetrics('module_create', 'success', Date.now() - t0);
    try {
      await this.createAuditLog({
        userId: request.userId ? Number(request.userId) : Number(1),
        orgId: undefined, // TODO: Add organizationId to request context
        userRole: request.roleName,
        actions: 'CREATE_MODULE',
        serviceName: 'auth-service',
        resourceType: 'MODULE',
        resourceId: Number(moduleRow.id),
        description: `Created module ${moduleRow.name}`,
        metadata: {},
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });
    } catch (logErr) {
      this.logger.error('audit log failed', logErr.stack);
    }

    return { success: true, message: 'Module created', module: moduleRow };
  }

  async bulkCreateModule(request: {
    modules: Array<{
      name: string;
      features: Array<{
        name: string;
        subFeatures?: Array<{ name: string }>;
      }>;
    }>;
    userId?: number;
    roleName?: string;
    ipAddress?: string;
    userAgent?: string;
  }) {
    const t0 = Date.now();
    let tx: Transaction;
    let committed = false;
    const createdModules = [];

    try {
      tx = await this.moduleModel.sequelize.transaction();

      for (const modIn of request.modules) {
        // 1) find or create module
        let mod = await this.moduleModel.findOne({
          where: { name: modIn.name },
          transaction: tx,
        });
        if (!mod) {
          mod = await this.moduleModel.create(
            { name: modIn.name },
            { transaction: tx }
          );
        }
        createdModules.push(mod);

        // 2) for each feature
        for (const featIn of modIn.features || []) {
          let feat = await this.featureModel.findOne({
            where: { name: featIn.name, moduleId: mod.id },
            transaction: tx,
          });
          if (!feat) {
            feat = await this.featureModel.create(
              { name: featIn.name, moduleId: mod.id },
              { transaction: tx }
            );
          }

          // 3) subFeatures
          for (const sfIn of featIn.subFeatures || []) {
            await this.subFeatureModel.findOrCreate({
              where: { name: sfIn.name, featureId: feat.id },
              defaults: { name: sfIn.name, featureId: feat.id },
              transaction: tx,
            });
          }
        }
      }

      await tx.commit();
      committed = true;
    } catch (dbErr) {
      if (tx && !committed) {
        try {
          await tx.rollback();
        } catch (rbErr) {
          this.logger.error('bulk rollback failed', rbErr);
        }
      }
      this.trackMetrics('module_bulk_create', 'failure', Date.now() - t0);
      this.logger.error(`bulkCreateModules db error`, dbErr.stack);
      throw new RpcException({
        code: status.INTERNAL,
        message: dbErr.message,
      });
    }

    // outside transaction
    this.trackMetrics('module_bulk_create', 'success', Date.now() - t0);
    for (const mod of createdModules) {
      try {
        await this.createAuditLog({
          userId: request.userId,
          orgId: undefined, // TODO: Add organizationId to request context
          userRole: request.roleName,
          actions: 'CREATE_MODULE',
          serviceName: 'auth-service',
          resourceType: 'MODULE',
          resourceId: Number(mod.id),
          description: `Bulk created module ${mod.name}`,
          metadata: {},
          ipAddress: request.ipAddress,
          userAgent: request.userAgent,
          source: 'grpc',
        });
      } catch (logErr) {
        this.logger.error('bulk audit log failed', logErr.stack);
      }
    }

    return {
      success: true,
      message: 'Modules created',
      modules: createdModules,
    };
  }

  async listModules(
    userId: number,
    roleName: string,
    ipAddress: string,
    userAgent: string
  ) {
    const t0 = Date.now();

    try {
      this.logger.log(`Listing modules`);

      const modules = await this.moduleModel.findAll({
        attributes: ['id', 'name'],
        include: [
          {
            model: this.featureModel,
            as: 'features',
            attributes: ['id', 'name'],
            required: false,
            include: [
              {
                model: this.subFeatureModel,
                as: 'subFeatures',
                attributes: ['id', 'name'],
                required: false,
              },
            ],
          },
        ],
        order: [
          ['name', 'ASC'],
          [{ model: this.featureModel, as: 'features' }, 'name', 'ASC'],
          [
            { model: this.featureModel, as: 'features' },
            { model: this.subFeatureModel, as: 'subFeatures' },
            'name',
            'ASC',
          ],
        ],
      });

      const mapped = modules.map((mod) => {
        const m = mod.get({ plain: true }) as any;
        return {
          id: m.id,
          name: m.name,
          features: (m.features || []).map((f: any) => ({
            id: f.id,
            name: f.name,
            subFeatures: (f.subFeatures || []).map((sf: any) => ({
              id: sf.id,
              name: sf.name,
            })),
          })),
        };
      });

      // success metrics
      this.trackMetrics('module_list', 'success', Date.now() - t0);

      // audit log
      try {
        await this.createAuditLog({
          userId,
          userRole: roleName,
          actions: 'LIST_MODULES',
          serviceName: 'auth-service',
          resourceType: 'MODULE',
          resourceId: 0, // no single resource
          description: `Listed ${mapped.length} modules`,
          metadata: { count: mapped.length.toString() },
          ipAddress,
          userAgent,
          source: 'grpc',
        });
      } catch (logErr) {
        this.logger.error('listModules audit log failed', logErr.stack);
      }

      return {
        success: true,
        message: 'Modules listed',
        modules: mapped,
      };
    } catch (error) {
      // failure metrics
      this.trackMetrics('module_list', 'failure', Date.now() - t0);

      this.logger.error(
        `Failed to list modules: ${error.message}`,
        error.stack
      );
      // for gRPC services you might throw a RpcException; if you want to keep returning a DTO:
      return {
        success: false,
        message: error.message || 'Failed to list modules',
      };
    }
  }
  // #endregion
  // --- Department ---
  // #region
  async createDepartments(request: {
    departments: Array<{ name: string; parent: string }>;
    userId?: number;
    roleName?: string;
    ipAddress?: string;
    userAgent?: string;
  }): Promise<{ success: boolean; message: string; departments: any[] }> {
    const t0 = Date.now();
    let tx = null;
    const processed: any[] = [];

    try {
      tx = await this.departmentModel.sequelize.transaction();
      this.logger.debug(
        `createDepartments: ${request.departments.length} departments`
      );
      for (const info of request.departments) {
        // 1) find or create parent (if specified)
        let parentDept = null;
        if (info.parent) {
          parentDept = await this.departmentModel.findOne({
            where: { name: info.parent },
            transaction: tx,
          });
          if (!parentDept) {
            parentDept = await this.departmentModel.create(
              { name: info.parent, parentId: null },
              { transaction: tx }
            );
          }
          this.logger.debug(
            `Parent department found/created: ${parentDept.name}`
          );
        }

        // 2) find or create the child under that parent (or root if no parent)
        const parentId = parentDept ? parentDept.id : null;
        let dept = await this.departmentModel.findOne({
          where: { name: info.name, parentId },
          transaction: tx,
        });
        if (!dept) {
          dept = await this.departmentModel.create(
            { name: info.name, parentId },
            { transaction: tx }
          );
        }

        processed.push(dept);
      }

      await tx.commit();

      // track bulk success
      this.trackMetrics('departments_bulk_create', 'success', Date.now() - t0);

      // audit each department processed
      for (const dept of processed) {
        try {
          await this.createAuditLog({
            userId: request.userId || null,
            userRole: request.roleName || '',
            actions: 'CREATE_OR_SKIP_DEPARTMENT',
            serviceName: 'auth-service',
            resourceType: 'DEPARTMENT',
            resourceId: Number(dept.id),
            description:
              `Processed department "${dept.name}"` +
              (dept.parentId ? ` under parent ID ${dept.parentId}` : ''),
            metadata: { parentId: dept.parentId },
            ipAddress: request.ipAddress || '',
            userAgent: request.userAgent || '',
            source: 'grpc',
          });
        } catch (logErr) {
          this.logger.error('department audit log failed', logErr.stack);
        }
      }

      this.logger.debug(
        `createDepartments: ${JSON.stringify(processed, null, 2)}`
      );
      return {
        success: true,
        message: 'Departments processed',
        departments: processed.map((d) => d.get({ plain: true })),
      };
    } catch (err) {
      if (tx) {
        try {
          await tx.rollback();
        } catch {}
      }
      this.trackMetrics('departments_bulk_create', 'failure', Date.now() - t0);
      this.logger.error(`createDepartments failed: ${err.message}`, err.stack);
      throw new RpcException({
        code: status.INTERNAL,
        message: err.message,
      });
    }
  }

  // ToDo - Wait for design completion
  async assignDepartment(request: any): Promise<any> {
    const startTime = Date.now();
    let transaction;
    try {
      this.logger.log(
        `Assigning departmentId=${request.departmentId} to userId=${request.userId}`
      );
      transaction = await this.userDepartmentModel.sequelize.transaction();

      const user = await this.userModel.findByPk(request.userId, {
        transaction,
      });
      if (!user) {
        await transaction.rollback();
        return { success: false, message: 'User not found' };
      }
      const department = await this.departmentModel.findByPk(
        request.departmentId,
        { transaction }
      );
      if (!department) {
        await transaction.rollback();
        return { success: false, message: 'Department not found' };
      }

      const existing = await this.userDepartmentModel.findOne({
        where: { userId: request.userId, departmentId: request.departmentId },
        transaction,
      });
      if (existing) {
        await transaction.rollback();
        return {
          success: false,
          message: 'User already assigned to this department',
        };
      }

      await this.userDepartmentModel.create(
        {
          userId: request.userId,
          departmentId: request.departmentId,
        },
        { transaction }
      );

      await transaction.commit();
      transaction = null;

      // Track metrics
      this.trackMetrics('department_assign', 'success', Date.now() - startTime);

      // Audit log
      await this.createAuditLog({
        userId: request.userId,
        userRole: request.roleName,
        actions: 'ASSIGN_DEPARTMENT',
        serviceName: 'auth-service',
        resourceType: 'DEPARTMENT',
        resourceId: request.departmentId,
        description: `Assigned departmentId ${request.departmentId} to userId ${request.userId}`,
        metadata: {},
        ipAddress: request.ipAddress || '',
        userAgent: request.userAgent || '',
        source: 'grpc',
      });

      return { success: true, message: 'Department assigned to user' };
    } catch (error) {
      if (transaction) await transaction.rollback();
      this.trackMetrics('department_assign', 'failure', Date.now() - startTime);
      this.logger.error(
        `Failed to assign department: ${error.message}`,
        error.stack
      );
      return { success: false, message: error.message };
    }
  }

  async listDepartments(request: {
    userId?: number;
    roleName?: string;
    ipAddress?: string;
    userAgent?: string;
  }) {
    const t0 = Date.now();
    try {
      this.logger.log(`Listing departments (building full tree)`);

      // 1) Fetch *all* departments (flat list for manual tree building)
      const rows = await this.departmentModel.findAll({
        attributes: [
          'id',
          'name',
          'parentId',
          'organizationId',
          'createdAt',
          'updatedAt',
        ],
        order: [['name', 'ASC']],
      });

      // 2) Convert to plain JSON objects
      const depts = rows.map((r) => {
        const plain = r.get({ plain: true }) as any;
        return {
          id: plain.id,
          name: plain.name,
          parentId: plain.parentId,
          organizationId: plain.organizationId,
          children: [], // Will be populated by tree building logic
          createdAt: plain.createdAt,
          updatedAt: plain.updatedAt,
        };
      });

      // 3) Build proper tree structure (only root departments with nested children)
      const map = new Map<number, (typeof depts)[0]>();

      // Initialize all departments with empty children arrays and create lookup map
      depts.forEach((d) => {
        d.children = [];
        map.set(d.id, d);
      });

      // Build parent-child relationships
      depts.forEach((dept) => {
        if (dept.parentId !== null && dept.parentId !== undefined) {
          const parent = map.get(dept.parentId);
          if (parent) {
            parent.children.push(dept);
          }
        }
      });

      // Filter to get only root departments (those with no parent)
      const roots = depts.filter((dept) => {
        const hasParent =
          dept.parentId !== null &&
          dept.parentId !== undefined &&
          dept.parentId !== 0;
        return !hasParent;
      });

      // 5) Metrics & audit
      this.trackMetrics('department_list', 'success', Date.now() - t0);
      await this.createAuditLog({
        userId: request.userId,
        userRole: request.roleName,
        actions: 'LIST_DEPARTMENTS',
        serviceName: 'auth-service',
        resourceType: 'DEPARTMENT',
        resourceId: 0,
        description: `Listed ${roots.length} root departments (total ${depts.length})`,
        metadata: {
          roots: roots.length.toString(),
          total: depts.length.toString(),
        },
        ipAddress: request.ipAddress || '',
        userAgent: request.userAgent || '',
        source: 'grpc',
      }).catch((err) => {
        this.logger.error('listDepartments audit log failed', err.stack);
      });

      return {
        success: true,
        message: 'Departments retrieved with hierarchy',
        departments: roots, // Return only root departments with nested children
      };
    } catch (error) {
      this.trackMetrics('department_list', 'failure', Date.now() - t0);
      this.logger.error(
        `Failed to list departments: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: error.message || 'Failed to list departments',
      };
    }
  }

  async listDepartmentsWithUsers(request: {
    userId?: number;
    roleName?: string;
    ipAddress?: string;
    userAgent?: string;
  }) {
    const t0 = Date.now();

    try {
      this.logger.log(`Listing departments with users (full tree)`);

      const allDepts = await this.departmentModel.findAll({
        attributes: ['id', 'name', 'parentId'],
        include: [
          {
            model: this.departmentModel,
            as: 'children',
            attributes: ['id', 'name', 'parentId'],
            required: false,
          },
          {
            model: this.userModel,
            as: 'users',
            attributes: ['id', 'name', 'email'],
            through: { attributes: [] },
            required: false,
          },
        ],
        order: [
          ['name', 'ASC'],
          [{ model: this.userModel, as: 'users' }, 'name', 'ASC'],
        ],
      });

      // build lookup & init
      const deptMap = new Map<number, (typeof allDepts)[0]>();
      allDepts.forEach((dept) => {
        dept.children = dept.children ?? [];
        dept.users = dept.users ?? [];
        deptMap.set(Number(dept.id), dept);
      });

      // re-parent
      const roots: typeof allDepts = [];
      allDepts.forEach((dept) => {
        if (dept.parentId == null) {
          roots.push(dept);
        } else {
          const p = deptMap.get(dept.parentId);
          if (p) p.children!.push(dept);
        }
      });

      // 4) Metrics & audit
      this.trackMetrics(
        'department_list_with_users',
        'success',
        Date.now() - t0
      );
      try {
        await this.createAuditLog({
          userId: request.userId,
          userRole: request.roleName,
          actions: 'LIST_DEPARTMENTS_WITH_USERS',
          serviceName: 'auth-service',
          resourceType: 'DEPARTMENT',
          resourceId: 0,
          description: `Listed ${roots.length} root departments (total ${allDepts.length}) with users`,
          metadata: {
            roots: roots.length.toString(),
            total: allDepts.length.toString(),
          },
          ipAddress: request.ipAddress || '',
          userAgent: request.userAgent || '',
          source: 'grpc',
        });
      } catch (logErr) {
        this.logger.error(
          'listDepartmentsWithUsers audit log failed',
          logErr.stack
        );
      }

      // **SAFE** serialize into plain JSON
      const serialize = (node: (typeof allDepts)[0]) => ({
        id: node.id,
        name: node.name,
        parentId: node.parentId ?? undefined,
        users: (node.users ?? []).map((u) => ({
          id: u.id,
          name: u.name,
          email: u.email,
        })),
        children: (node.children ?? []).map(serialize),
      });

      this.logger.log(
        `Departments listed with users`,
        JSON.stringify(roots.map(serialize), null, 2)
      );

      return {
        success: true,
        message: 'Departments listed',
        departments: roots.map(serialize),
      };
    } catch (error) {
      // failure metrics
      this.trackMetrics(
        'department_list_with_users',
        'failure',
        Date.now() - t0
      );
      this.logger.error(
        `Failed to list departments with users: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: error.message || 'Failed to list departments',
        departments: [],
      };
    }
  }
  // #endregion

  // ----------- Roles ---------------
  // #region
  async getRoleDetails(
    req: GetRoleDetailsRequest
  ): Promise<RoleDetailsResponse> {
    const t0 = Date.now();
    this.logger.log(`Fetching details for role ${req.name}`);

    // 1) load the Role → Users → Departments
    const role = await this.roleModel.findOne({
      where: { name: req.name },
      include: [
        {
          model: this.userModel,
          as: 'users',
          attributes: ['id', 'name', 'email'],
          through: { attributes: [] },
          include: [
            {
              model: this.departmentModel,
              as: 'departments',
              attributes: ['id', 'name'],
              through: { attributes: [] },
            },
          ],
        },
      ],
    });
    if (!role) {
      throw new RpcException({
        code: status.NOT_FOUND,
        message: 'Role not found',
      });
    }

    // 2) group per‐department including id
    const deptMap = new Map<
      number,
      {
        name: string;
        users: Array<{ id: number; name: string; email: string }>;
      }
    >();
    for (const user of (role as any).users as any[]) {
      const u = user.get({ plain: true }) as any;
      for (const d of u.departments as any[]) {
        const depId = Number(d.id);
        if (!deptMap.has(depId)) {
          deptMap.set(depId, { name: d.name, users: [] });
        }
        deptMap.get(depId)!.users.push({
          id: Number(u.id),
          name: u.name,
          email: u.email,
        });
      }
    }
    const department = Array.from(deptMap.entries()).map(
      ([id, { name, users }]) => ({
        id,
        name,
        users,
      })
    );

    // 3) load all modules → features → subFeatures
    const allModules = await this.moduleModel.findAll({
      include: [
        {
          model: this.featureModel,
          as: 'features',
          include: [{ model: this.subFeatureModel, as: 'subFeatures' }],
        },
      ],
      order: [
        ['name', 'ASC'],
        [{ model: this.featureModel, as: 'features' }, 'name', 'ASC'],
        [
          { model: this.featureModel, as: 'features' },
          { model: this.subFeatureModel, as: 'subFeatures' },
          'name',
          'ASC',
        ],
      ],
    });

    // 4) fetch this role’s explicit allow‐lists
    const featPerms = await this.roleFeaturePermissionModel.findAll({
      where: { roleId: role.id, isAllowed: true },
    });
    const subFeatPerms = await this.roleSubFeaturePermissionModel.findAll({
      where: { roleId: role.id, isAllowed: true },
    });
    const allowedFeatIds = new Set(featPerms.map((p) => Number(p.featureId)));
    const allowedSubFeatIds = new Set(
      subFeatPerms.map((p) => Number(p.subFeatureId))
    );

    // 5) assemble modules output, including all IDs
    const modules = allModules.map((m) => {
      const mod = m.get({ plain: true }) as any;
      return {
        id: Number(mod.id),
        module: mod.name,
        features: (mod.features || []).map((f: any) => ({
          id: Number(f.id),
          feature: f.name,
          permissions: allowedFeatIds.has(Number(f.id)),
          subFeatures: (f.subFeatures || []).map((sf: any) => ({
            id: Number(sf.id),
            subFeature: sf.name,
            permissions: allowedSubFeatIds.has(Number(sf.id)),
          })),
        })),
      };
    });

    // 6) metrics & audit
    this.trackMetrics('role_details', 'success', Date.now() - t0);
    try {
      await this.createAuditLog({
        userId: req.getArgs.userId,
        userRole: req.getArgs.roleName,
        actions: 'GET_ROLE_DETAILS',
        serviceName: 'auth-service',
        resourceType: 'ROLE',
        resourceId: Number(role.id),
        description: `Fetched details for role ${req.name}`,
        metadata: {},
        ipAddress: req.getArgs.ipAddress || '',
        userAgent: req.getArgs.userAgent || '',
        source: 'grpc',
      });
    } catch (logErr) {
      this.logger.error('roleDetails audit log failed', logErr.stack);
    }

    const serialized = JSON.stringify(
      {
        role: role.name,
        department,
        modules,
      },
      null,
      2
    );
    // this.logger.log(`Role details fetched`, serialized);

    // final payload
    return {
      success: true,
      message: 'Role details retrieved',
      role: {
        id: Number(role.id),
        role: role.name,
        department,
        modules,
      },
    };
  }

  async createOrUpdateRoleWithDetails(
    req: CreateRoleWithDetailsRequest
  ): Promise<CreateRoleResponse> {
    const t0 = Date.now();
    const tx = await this.roleModel.sequelize.transaction();

    try {
      this.logger.log(
        `${req.id ? 'Updating' : 'Creating'} role ${req.name} (${req.id})`
      );

      // 1) find or create the Role record
      let role: Role;
      if (req.id) {
        role = await this.roleModel.findByPk(req.id, { transaction: tx });
        if (!role) {
          throw new RpcException({
            code: status.NOT_FOUND,
            message: `Role ${req.id} not found`,
          });
        }

        // update its name
        role.name = req.name;
        await role.save({ transaction: tx });

        // clear out old feature/sub‐feature permissions & user links
        await this.roleFeaturePermissionModel.destroy({
          where: { roleId: role.id },
          transaction: tx,
        });
        await this.roleSubFeaturePermissionModel.destroy({
          where: { roleId: role.id },
          transaction: tx,
        });
        await (role as any).setUsers([], { transaction: tx });
      } else {
        role = await this.roleModel.create(
          { name: req.name },
          { transaction: tx }
        );
      }

      // 2) departments & users
      for (const deptInfo of req.departments || []) {
        const dept = await this.departmentModel.findByPk(deptInfo.id, {
          transaction: tx,
        });
        if (!dept) {
          throw new RpcException({
            code: status.NOT_FOUND,
            message: `Department ${deptInfo.id} not found`,
          });
        }

        for (const u of deptInfo.users || []) {
          const user = await this.userModel.findByPk(u.id, { transaction: tx });
          if (!user) {
            throw new RpcException({
              code: status.NOT_FOUND,
              message: `User ${u.id} not found`,
            });
          }

          // upsert into user_roles
          await this.userRoleModel.upsert(
            { userId: user.id, roleId: role.id },
            {
              transaction: tx,
              conflictFields: ['userId', 'roleId'],
            }
          );

          // ensure user ↔ department
          await this.userDepartmentModel.findOrCreate({
            where: { userId: user.id, departmentId: dept.id },
            transaction: tx,
          });
        }
      }

      // 3) feature & sub‐feature permissions
      for (const m of req.modules || []) {
        for (const f of m.features || []) {
          await this.roleFeaturePermissionModel.upsert(
            {
              roleId: role.id,
              featureId: f.id,
              isAllowed: f.permissions,
            },
            {
              transaction: tx,
              conflictFields: ['roleId', 'featureId'],
            }
          );

          for (const sf of f.subFeatures || []) {
            await this.roleSubFeaturePermissionModel.upsert(
              {
                roleId: role.id,
                subFeatureId: sf.id,
                isAllowed: sf.permissions,
              },
              {
                transaction: tx,
                conflictFields: ['roleId', 'subFeatureId'],
              }
            );
          }
        }
      }

      // 4) commit + metrics + audit
      await tx.commit();
      this.trackMetrics('role_create_or_update', 'success', Date.now() - t0);
      await this.createAuditLog({
        userId: req.getArgs.userId,
        userRole: req.getArgs.roleName,
        actions: req.id ? 'UPDATE_ROLE' : 'CREATE_ROLE',
        serviceName: 'auth-service',
        resourceType: 'ROLE',
        resourceId: Number(role.id),
        description: `${req.id ? 'Updated' : 'Created'} role ${req.name}`,
        metadata: {},
        ipAddress: req.getArgs.ipAddress,
        userAgent: req.getArgs.userAgent,
        source: 'grpc',
      });

      // 5) Fetch complete role details to return in response
      const roleDetails = await this.getRoleDetailsForResponse(Number(role.id));

      return {
        success: true,
        message: req.id ? 'Role updated' : 'Role created',
        role: roleDetails,
      };
    } catch (error) {
      await tx.rollback();
      this.trackMetrics('role_create_or_update', 'failure', Date.now() - t0);
      this.logger.error(
        `Failed to ${req.id ? 'update' : 'create'} role: ${error.message}`,
        error.stack
      );
      return { success: false, message: error.message };
    }
  }

  private async getRoleDetailsForResponse(roleId: number) {
    // Load the role with all its relationships
    const role = await this.roleModel.findByPk(roleId, {
      include: [
        {
          model: this.userModel,
          as: 'users',
          attributes: ['id', 'name', 'email'],
          through: { attributes: [] },
          include: [
            {
              model: this.departmentModel,
              as: 'departments',
              attributes: ['id', 'name'],
              through: { attributes: [] },
            },
          ],
        },
      ],
    });

    if (!role) {
      throw new Error(`Role with ID ${roleId} not found`);
    }

    // Get all modules with features and sub-features
    const allModules = await this.moduleModel.findAll({
      include: [
        {
          model: this.featureModel,
          as: 'features',
          include: [
            {
              model: this.subFeatureModel,
              as: 'subFeatures',
            },
          ],
        },
      ],
    });

    // Get role's allowed features and sub-features
    const allowedFeatures = await this.roleFeaturePermissionModel.findAll({
      where: { roleId, isAllowed: true },
    });

    const allowedSubFeatures = await this.roleSubFeaturePermissionModel.findAll(
      {
        where: { roleId, isAllowed: true },
      }
    );

    const allowedFeatureIds = new Set(
      allowedFeatures.map((f) => Number(f.featureId))
    );
    const allowedSubFeatureIds = new Set(
      allowedSubFeatures.map((sf) => Number(sf.subFeatureId))
    );

    // Build department structure
    const deptMap = new Map();
    for (const user of role.users || []) {
      for (const dept of user.departments || []) {
        const deptId = Number(dept.id);
        if (!deptMap.has(deptId)) {
          deptMap.set(deptId, {
            id: deptId,
            name: dept.name,
            users: [],
          });
        }
        deptMap.get(deptId).users.push({
          id: Number(user.id),
          name: user.name,
          email: user.email,
        });
      }
    }

    const departments = Array.from(deptMap.values());

    // Build modules structure with permissions
    const modules = allModules.map((m) => {
      const mod = m.get({ plain: true }) as any;
      return {
        id: Number(mod.id),
        module: mod.name,
        features: (mod.features || []).map((f: any) => {
          const fid = Number(f.id);
          const featAllowed = allowedFeatureIds.has(fid);
          return {
            id: fid,
            feature: f.name,
            permissions: featAllowed,
            subFeatures: (f.subFeatures || []).map((sf: any) => {
              const sfid = Number(sf.id);
              return {
                id: sfid,
                subFeature: sf.name,
                permissions: allowedSubFeatureIds.has(sfid),
              };
            }),
          };
        }),
      };
    });

    return {
      id: Number(role.id),
      name: role.name,
      description: role.description,
      departments,
      modules,
    };
  }

  async getRolesWithDetails(request: {
    userId?: number;
    roleName?: string;
    ipAddress?: string;
    userAgent?: string;
  }): Promise<GetRolesWithDetailsResponse> {
    const { userId, roleName, ipAddress, userAgent } = request;
    const t0 = Date.now();

    try {
      this.logger.log(`Getting all roles with full details`);

      // 1) load all roles → users → departments
      const roles = await this.roleModel.findAll({
        attributes: ['id', 'name'],
        include: [
          {
            model: this.userModel,
            as: 'users',
            attributes: ['id', 'name', 'email'],
            through: { attributes: [] },
            required: false,
            include: [
              {
                model: this.departmentModel,
                as: 'departments',
                attributes: ['id', 'name'],
                through: { attributes: [] },
                required: false,
              },
            ],
          },
        ],
        order: [['name', 'ASC']],
      });

      // 2) load the full module → feature → subFeature tree
      const allModules = await this.moduleModel.findAll({
        attributes: ['id', 'name'],
        include: [
          {
            model: this.featureModel,
            as: 'features',
            attributes: ['id', 'name'],
            include: [
              {
                model: this.subFeatureModel,
                as: 'subFeatures',
                attributes: ['id', 'name'],
              },
            ],
          },
        ],
        order: [
          ['name', 'ASC'],
          [{ model: this.featureModel, as: 'features' }, 'name', 'ASC'],
          [
            { model: this.featureModel, as: 'features' },
            { model: this.subFeatureModel, as: 'subFeatures' },
            'name',
            'ASC',
          ],
        ],
      });

      // 3) load every role’s allowed features & subFeatures in one go
      const featPerms = await this.roleFeaturePermissionModel.findAll({
        where: { isAllowed: true },
      });
      const subFeatPerms = await this.roleSubFeaturePermissionModel.findAll({
        where: { isAllowed: true },
      });

      // 4) build lookup tables
      const allowedFeatByRole = new Map<number, Set<number>>();
      featPerms.forEach((p) => {
        if (!allowedFeatByRole.has(Number(p.roleId)))
          allowedFeatByRole.set(Number(p.roleId), new Set());
        allowedFeatByRole.get(Number(p.roleId))!.add(Number(p.featureId));
      });
      const allowedSubFeatByRole = new Map<number, Set<number>>();
      subFeatPerms.forEach((p) => {
        if (!allowedSubFeatByRole.has(Number(p.roleId)))
          allowedSubFeatByRole.set(Number(p.roleId), new Set());
        allowedSubFeatByRole.get(Number(p.roleId))!.add(Number(p.subFeatureId));
      });

      // 5) assemble per-role details
      const mapped = roles.map((roleInstance) => {
        const r = roleInstance.get({ plain: true }) as any;
        const roleId = Number(r.id);

        // 5a) group users by department
        const deptMap = new Map<
          number,
          { id: number; name: string; users: any[] }
        >();
        (r.users || []).forEach((u: any) => {
          (u.departments || []).forEach((d: any) => {
            const did = Number(d.id);
            if (!deptMap.has(did)) {
              deptMap.set(did, { id: did, name: d.name, users: [] });
            }
            deptMap.get(did)!.users.push({
              id: Number(u.id),
              name: u.name,
              email: u.email,
            });
          });
        });
        const department = Array.from(deptMap.values());

        // 5b) build modules → features → subFeatures with permissions
        const modules = allModules.map((m) => {
          const mod = m.get({ plain: true }) as any;
          return {
            id: Number(mod.id),
            module: mod.name,
            features: (mod.features || []).map((f: any) => {
              const fid = Number(f.id);
              const featAllowed =
                allowedFeatByRole.get(roleId)?.has(fid) ?? false;
              return {
                id: fid,
                feature: f.name,
                permissions: featAllowed,
                subFeatures: (f.subFeatures || []).map((sf: any) => {
                  const sfid = Number(sf.id);
                  return {
                    id: sfid,
                    subFeature: sf.name,
                    permissions:
                      allowedSubFeatByRole.get(roleId)?.has(sfid) ?? false,
                  };
                }),
              };
            }),
          };
        });

        return {
          id: roleId,
          role: r.name,
          department,
          modules,
        };
      });

      // 6) metrics & audit
      this.trackMetrics('roles_with_details', 'success', Date.now() - t0);
      try {
        await this.createAuditLog({
          userId,
          userRole: roleName,
          actions: 'GET_ROLES_WITH_DETAILS',
          serviceName: 'auth-service',
          resourceType: 'ROLE',
          resourceId: 0,
          description: `Retrieved ${mapped.length} roles with full details`,
          metadata: { count: mapped.length.toString() },
          ipAddress,
          userAgent,
          source: 'grpc',
        });
      } catch (logErr) {
        this.logger.error('getRolesWithDetails audit log failed', logErr.stack);
      }
      // this.logger.log(
      //   `Roles with details fetched`,
      //   JSON.stringify(mapped, null, 2)
      // );

      return {
        success: true,
        message: 'Roles, departments, users & permissions retrieved',
        roles: mapped,
      };
    } catch (error) {
      this.trackMetrics('roles_with_details', 'failure', Date.now() - t0);
      this.logger.error(
        `Failed to get roles with details: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: error.message || 'Failed to get roles with details',
        roles: [],
      };
    }
  }

  async updateRole(request: UpdateRoleRequest): Promise<UpdateRoleResponse> {
    const { id, roleName, getArgs } = request;
    const t0 = Date.now();

    try {
      this.logger.log(
        `Updating role: ${id} to ${JSON.stringify(roleName, null, 2)}`
      );

      // 1) check if role exists
      const role = await this.roleModel.findByPk(id);
      if (!role) {
        return {
          success: false,
          message: 'Role not found',
        };
      }

      // 2) update role name
      await this.roleModel.update(
        { name: roleName },
        {
          where: { id },
        }
      );

      // 3) metrics & audit
      this.trackMetrics('role_update', 'success', Date.now() - t0);
      await this.createAuditLog({
        userId: getArgs.userId,
        userRole: getArgs.roleName,
        actions: 'UPDATE_ROLE',
        serviceName: 'auth-service',
        resourceType: 'ROLE',
        resourceId: id,
        description: `Updated role ${id} to ${roleName}`,
        metadata: {},
        ipAddress: getArgs.ipAddress,
        userAgent: getArgs.userAgent,
        source: 'grpc',
      });

      return {
        success: true,
        message: 'Role updated',
      };
    } catch (error) {
      this.trackMetrics('role_update', 'failure', Date.now() - t0);
      this.logger.error(`Failed to update role: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to update role',
      };
    }
  }

  async deleteRole(request: DeleteRoleRequest): Promise<DeleteRoleResponse> {
    const { id, getArgs } = request;
    const t0 = Date.now();

    try {
      this.logger.log(`Deleting role: ${id}`);

      // 1) fetch the role (with its users so we can unlink them)
      const role = await this.roleModel.findByPk(id, {
        include: [{ model: this.userModel, as: 'users' }],
      });
      if (!role) {
        return {
          success: false,
          message: 'Role not found',
        };
      }

      // 2) delete all feature-permission associations for this role
      await this.roleFeaturePermissionModel.destroy({
        where: { roleId: id },
      });
      // 3) delete all subFeature-permission associations
      await this.roleSubFeaturePermissionModel.destroy({
        where: { roleId: id },
      });
      // 4) unlink this role from any users
      //    (assumes a .setUsers([]) magic method from your belongsToMany setup)
      await (role as any).setUsers([]);

      // 5) finally delete the role itself
      await role.destroy();

      // 6) metrics & audit
      this.trackMetrics('role_delete', 'success', Date.now() - t0);
      await this.createAuditLog({
        userId: getArgs.userId,
        userRole: getArgs.roleName,
        actions: 'DELETE_ROLE',
        serviceName: 'auth-service',
        resourceType: 'ROLE',
        resourceId: id,
        description: `Deleted role ${id}`,
        metadata: {},
        ipAddress: getArgs.ipAddress,
        userAgent: getArgs.userAgent,
        source: 'grpc',
      });

      return {
        success: true,
        message: 'Role deleted',
      };
    } catch (error) {
      this.trackMetrics('role_delete', 'failure', Date.now() - t0);
      this.logger.error(`Failed to delete role: ${error.message}`, error.stack);
      return {
        success: false,
        message: error.message || 'Failed to delete role',
      };
    }
  }

  // #endregion

  // ------------ University ----------------
  // #region
  async createUniversity(request: any): Promise<any> {
    try {
      this.logger.log(`Creating university: ${request.name}`);
      // ToDo: create university user
      const hashedPassword = await bcrypt.hash('DefaultPassword123!', 10);

      const user = await this.userModel.create({
        name: request.name,
        email: request.email,
        password: hashedPassword,
        phone: request.primaryContactNumber,
        status: 'active',
        organizationId: 1,
      });

      // ToDo: create univeristy organization
      const organization = await this.organizationModel.create({
        name: request.name,
        type: 'university',
        description: `Organization for ${request.name}`,
      });

      return {
        success: true,
        message: 'University created',
        organization: {
          id: organization.id,
          name: organization.name,
          type: organization.type,
          description: organization.description,
        },
        user,
      };
    } catch (error) {
      this.logger.error(
        `Failed to create university: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: error.message || 'Failed to create university',
      };
    }
  }

  // #endregion

  // ------------ Agency ----------------
  // #region
  async createAgency(request: any): Promise<any> {
    try {
      this.logger.log(`Creating agency: ${request.name}`);
      // ToDo: create agency user
      const user = await this.userModel.create({
        name: request.name,
        email: request.email,
        password: 'DefaultPassword123!',
        phone: request.primaryContactNumber,
        status: 'active',
        organizationId: 1,
      });

      // ToDo: create agency organization
      const organization = await this.organizationModel.create({
        name: request.name,
        type: 'agency',
        description: `Organization for ${request.name}`,
      });

      return {
        success: true,
        message: 'Agency created',
        organization: {
          id: organization.id,
          name: organization.name,
          type: organization.type,
          description: organization.description,
        },
        user,
      };
    } catch (error) {
      this.logger.error(
        `Failed to create agency: ${error.message}`,
        error.stack
      );
      return {
        success: false,
        message: error.message || 'Failed to create agency',
      };
    }
  }
  // #endregion
}
