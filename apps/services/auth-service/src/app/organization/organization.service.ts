import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op, UniqueConstraintError } from 'sequelize';
import { firstValueFrom } from 'rxjs';
import { Organization } from './organization.model';
import { AuditClientService } from '../audit/audit.service';
import { AppService } from '../app.service';
import {
  CreateOrganizationRequest,
  CreateOrganizationResponse,
  GetOrganizationRequest,
  GetOrganizationResponse,
  UpdateOrganizationRequest,
  UpdateOrganizationResponse,
  DeleteOrganizationRequest,
  DeleteOrganizationResponse,
  ListOrganizationsRequest,
  ListOrganizationsResponse,
  OrganizationInfo,
  CreateOrganizationData,
  UpdateOrganizationData,
  ORGANIZATION_TYPES,
} from './organization.interface';
import { CreateAuditLogRequest } from '../auth/auth.interfaces';

@Injectable()
export class OrganizationService {
  private readonly logger = new Logger(OrganizationService.name);

  constructor(
    @InjectModel(Organization)
    private readonly organizationModel: typeof Organization,
    private readonly auditClient: AuditClientService,
    private readonly appService: AppService
  ) {}

  // —1) exactly as in AuthService
  private async createAuditLog(payload: CreateAuditLogRequest) {
    const start = Date.now();
    try {
      await firstValueFrom(this.auditClient.createAuditLog(payload));
      this.appService.trackAuditLogRequest(
        'create',
        'success',
        (Date.now() - start) / 1000
      );
    } catch (error) {
      this.appService.trackAuditLogRequest(
        'create',
        'error',
        (Date.now() - start) / 1000
      );
      this.logger.error('Failed to create audit log', error);
    }
  }

  // —2) same metrics helper
  private trackMetrics(
    operation: string,
    status: 'success' | 'failure' | 'error',
    duration: number
  ) {
    try {
      if (operation === 'token')
        this.appService.trackTokenOperation('generate', duration);
      else if (operation === 'login')
        this.appService.trackLoginAttempt(
          status,
          status === 'success' ? 'valid_credentials' : 'invalid_credentials'
        );
      else if (operation === 'auth')
        this.appService.trackAuthorization('api', 'access', status);

      // custom ops for org can still use generic "auth" or omit
    } catch (err) {
      this.logger.warn(`Metrics error: ${err.message}`);
    }
  }

  async createOrganization(
    request: CreateOrganizationRequest
  ): Promise<CreateOrganizationResponse> {
    const startTime = Date.now();
    try {
      this.logger.log(`Creating organization: ${request.name}`);

      if (!ORGANIZATION_TYPES.includes(request.type as any)) {
        throw new BadRequestException(
          `Invalid organization type. Must be one of: ${ORGANIZATION_TYPES.join(
            ', '
          )}`
        );
      }

      const data: CreateOrganizationData = {
        name: request.name,
        type: request.type,
        imageUrl: request.imageUrl,
        description: request.description,
        website: request.website,
        address: request.address,
        country: request.country,
        state: request.state,
        city: request.city,
        postalCode: request.postalCode,
        phone: request.phone,
        email: request.email,
      };

      const organization = await this.organizationModel.create(data as any);

      // audit + metrics
      await this.createAuditLog({
        userId: Number(request.userId),
        orgId: organization.id,
        userRole: request.roleName,
        actions: 'CREATE_ORGANIZATION',
        serviceName: 'auth-service',
        resourceType: 'Organization',
        resourceId: Number(organization.id),
        description: `Created organization: ${organization.name}`,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });
      this.trackMetrics(
        'create_organization',
        'success',
        Date.now() - startTime
      );

      return {
        success: true,
        message: 'Organization created successfully',
        organization: this.mapToOrganizationInfo(organization),
      };
    } catch (error) {
      this.logger.error(
        `Error creating organization: ${error.message}`,
        error.stack
      );
      this.trackMetrics('create_organization', 'error', Date.now() - startTime);

      if (error instanceof UniqueConstraintError) {
        throw new ConflictException(
          'Organization with this name already exists'
        );
      }
      if (error instanceof BadRequestException) {
        throw error;
      }
      throw new Error('Failed to create organization');
    }
  }

  async getOrganization(
    request: GetOrganizationRequest
  ): Promise<GetOrganizationResponse> {
    const startTime = Date.now();
    try {
      this.logger.log(`Getting organization: ${request.id}`);
      const org = await this.organizationModel.findByPk(request.id);
      if (!org) throw new NotFoundException('Organization not found');

      await this.createAuditLog({
        userId: Number(request.userId),
        userRole: request.roleName,
        actions: 'VIEW_ORGANIZATION',
        serviceName: 'auth-service',
        resourceType: 'Organization',
        resourceId: Number(org.id),
        description: `Viewed organization: ${org.name}`,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });
      this.trackMetrics('get_organization', 'success', Date.now() - startTime);

      return {
        success: true,
        message: 'Organization retrieved successfully',
        organization: this.mapToOrganizationInfo(org),
      };
    } catch (error) {
      this.logger.error(
        `Error getting organization: ${error.message}`,
        error.stack
      );
      this.trackMetrics('get_organization', 'error', Date.now() - startTime);
      if (error instanceof NotFoundException) throw error;
      throw new Error('Failed to get organization');
    }
  }

  async updateOrganization(
    request: UpdateOrganizationRequest
  ): Promise<UpdateOrganizationResponse> {
    const startTime = Date.now();
    try {
      this.logger.log(`Updating organization: ${request.id}`);

      const org = await this.organizationModel.findByPk(request.id);
      if (!org) throw new NotFoundException('Organization not found');

      if (request.type && !ORGANIZATION_TYPES.includes(request.type as any)) {
        throw new BadRequestException(
          `Invalid organization type. Must be one of: ${ORGANIZATION_TYPES.join(
            ', '
          )}`
        );
      }

      const updateData: UpdateOrganizationData = {};
      Object.assign(updateData, request);

      await org.update(updateData);

      await this.createAuditLog({
        userId: Number(request.userId),
        userRole: request.roleName,
        actions: 'UPDATE_ORGANIZATION',
        serviceName: 'auth-service',
        resourceType: 'Organization',
        resourceId: Number(org.id),
        description: `Updated organization: ${org.name}`,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });
      this.trackMetrics(
        'update_organization',
        'success',
        Date.now() - startTime
      );

      return {
        success: true,
        message: 'Organization updated successfully',
        organization: this.mapToOrganizationInfo(org),
      };
    } catch (error) {
      this.logger.error(
        `Error updating organization: ${error.message}`,
        error.stack
      );
      this.trackMetrics('update_organization', 'error', Date.now() - startTime);

      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      if (error instanceof UniqueConstraintError) {
        throw new ConflictException(
          'Organization with this name already exists'
        );
      }
      throw new Error('Failed to update organization');
    }
  }

  async deleteOrganization(
    request: DeleteOrganizationRequest
  ): Promise<DeleteOrganizationResponse> {
    const startTime = Date.now();
    try {
      this.logger.log(`Deleting organization: ${request.id}`);
      const org = await this.organizationModel.findByPk(request.id);
      if (!org) throw new NotFoundException('Organization not found');

      await org.destroy();

      await this.createAuditLog({
        userId: Number(request.userId),
        userRole: request.roleName,
        actions: 'DELETE_ORGANIZATION',
        serviceName: 'auth-service',
        resourceType: 'Organization',
        resourceId: Number(org.id),
        description: `Deleted organization: ${org.name}`,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });
      this.trackMetrics(
        'delete_organization',
        'success',
        Date.now() - startTime
      );

      return { success: true, message: 'Organization deleted successfully' };
    } catch (error) {
      this.logger.error(
        `Error deleting organization: ${error.message}`,
        error.stack
      );
      this.trackMetrics('delete_organization', 'error', Date.now() - startTime);
      if (error instanceof NotFoundException) throw error;
      throw new Error('Failed to delete organization');
    }
  }

  async listOrganizations(
    request: ListOrganizationsRequest
  ): Promise<ListOrganizationsResponse> {
    const startTime = Date.now();
    try {
      this.logger.log('Listing organizations');
      const page = request.page || 1;
      const limit = request.limit || 10;
      const offset = (page - 1) * limit;

      const where: any = {};
      if (request.search) {
        where[Op.or] = [
          { name: { [Op.iLike]: `%${request.search}%` } },
          { description: { [Op.iLike]: `%${request.search}%` } },
          { email: { [Op.iLike]: `%${request.search}%` } },
        ];
      }
      if (request.type) where.type = request.type;

      const { rows, count } = await this.organizationModel.findAndCountAll({
        where,
        limit,
        offset,
        order: [['createdAt', 'DESC']],
      });

      await this.createAuditLog({
        userId: Number(request.userId),
        userRole: request.roleName,
        actions: 'LIST_ORGANIZATIONS',
        serviceName: 'auth-service',
        resourceType: 'Organization',
        resourceId: 0,
        description: `Listed organizations (page: ${page}, limit: ${limit})`,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });
      this.trackMetrics(
        'list_organizations',
        'success',
        Date.now() - startTime
      );

      return {
        success: true,
        message: 'Organizations retrieved successfully',
        organizations: rows.map((org) => this.mapToOrganizationInfo(org)),
        total: count,
        page,
        limit,
      };
    } catch (error) {
      this.logger.error(
        `Error listing organizations: ${error.message}`,
        error.stack
      );
      this.trackMetrics('list_organizations', 'error', Date.now() - startTime);
      throw new Error('Failed to list organizations');
    }
  }

  private mapToOrganizationInfo(organization: Organization): OrganizationInfo {
    return {
      id: organization.id,
      name: organization.name,
      type: organization.type,
      imageUrl: organization.imageUrl,
      description: organization.description,
      website: organization.website,
      address: organization.address,
      country: organization.country,
      state: organization.state,
      city: organization.city,
      postalCode: organization.postalCode,
      phone: organization.phone,
      email: organization.email,
      isActive: organization.isActive,
      createdAt: organization.createdAt,
      updatedAt: organization.updatedAt,
    };
  }
}
